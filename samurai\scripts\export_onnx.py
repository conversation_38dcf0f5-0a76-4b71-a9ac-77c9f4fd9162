import torch
import os
import sys

def main():
    try:
        # It's possible the 'sam2' module is not in the python path when running from scripts/
        # Let's add the project root to the path.
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sys.path.insert(0, project_root)
        print(f"Added {project_root} to Python path")

        from sam2.build_sam import build_sam2_video_predictor

        print("Loading model...")
        model_name = "base_plus"
        # Check if checkpoint exists
        checkpoint_path = f"sam2/checkpoints/sam2.1_hiera_{model_name}.pt"
        if not os.path.exists(checkpoint_path):
            print(f"Checkpoint file not found at: {checkpoint_path}")
            print("Please run 'cd checkpoints && ./download_ckpts.sh && cd ..' as mentioned in the README.")
            return

        model_cfg = f"configs/samurai/sam2.1_hiera_b+.yaml"
        
        print("Building SAM2 video predictor...")
        predictor = build_sam2_video_predictor(model_cfg, checkpoint_path, device="cpu")
        predictor.eval()
        model = predictor
        print("Model loaded successfully.")

        print("Extracting image encoder...")
        image_encoder = model.image_encoder

        image_size = model.image_size
        dummy_input = torch.randn(1, 3, image_size, image_size, device="cpu")
        print(f"Created dummy input of size {dummy_input.shape}")

        output_dir = "onnx_models"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"Created directory: {output_dir}")

        output_path = os.path.join(output_dir, "image_encoder.onnx")

        print(f"Exporting image_encoder to {output_path}...")
        torch.onnx.export(
            image_encoder,
            dummy_input,
            output_path,
            input_names=["input_image"],
            output_names=["image_features"],
            dynamic_axes={
                "input_image": {0: "batch_size"},
                "image_features": {0: "batch_size"},
            },
            opset_version=17
        )

        print(f"Image encoder exported successfully to {output_path}!")

    except Exception as e:
        print(f"An error occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

